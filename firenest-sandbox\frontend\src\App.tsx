import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from '@/stores/authStore'
import { AuthProvider } from '@/components/auth/AuthProvider'
import { Layout } from '@/components/layout/Layout'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

// Pages
import { LoginPage } from '@/pages/auth/LoginPage'
import { DashboardPage } from '@/pages/DashboardPage'
import { WorkspacesPage } from '@/pages/WorkspacesPage'
import { WorkspaceDetailPage } from '@/pages/WorkspaceDetailPage'
import { ProjectsPage } from '@/pages/ProjectsPage'
import { ProjectDetailPage } from '@/pages/ProjectDetailPage'
import { UploadPage } from '@/pages/UploadPage'
import { ModelsPage } from '@/pages/ModelsPage'
import { ModelDetailPage } from '@/pages/ModelDetailPage'
import { ModelsDashboard } from '@/pages/ModelsDashboard'
import { SimulationsPage } from '@/pages/SimulationsPage'
import { SimulationDetailPage } from '@/pages/SimulationDetailPage'
import { SettingsPage } from '@/pages/SettingsPage'

function App() {
  return (
    <AuthProvider>
      <AppRoutes />
    </AuthProvider>
  )
}

function AppRoutes() {
  const { isAuthenticated, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  return (
    <Layout>
      <Routes>
        {/* Dashboard */}
        <Route path="/" element={<DashboardPage />} />
        <Route path="/dashboard" element={<Navigate to="/" replace />} />

        {/* Workspaces */}
        <Route path="/workspaces" element={<WorkspacesPage />} />
        <Route path="/workspaces/:workspaceId" element={<WorkspaceDetailPage />} />

        {/* Projects */}
        <Route path="/projects" element={<ProjectsPage />} />
        <Route path="/projects/:projectId" element={<ProjectDetailPage />} />

        {/* Data Upload */}
        <Route path="/projects/:projectId/upload" element={<UploadPage />} />

        {/* Pricing Models */}
        <Route path="/projects/:projectId/models" element={<ModelsPage />} />
        <Route path="/models/:modelId" element={<ModelDetailPage />} />
        <Route path="/models" element={<ModelsDashboard />} />

        {/* Simulations */}
        <Route path="/projects/:projectId/simulations" element={<SimulationsPage />} />
        <Route path="/simulations/:simulationId" element={<SimulationDetailPage />} />
        <Route path="/simulations" element={<SimulationsPage />} />

        {/* Settings */}
        <Route path="/settings" element={<SettingsPage />} />

        {/* Catch all */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Layout>
  )
}

export default App
