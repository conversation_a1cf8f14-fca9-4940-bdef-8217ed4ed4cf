/**
 * Authentication Middleware
 * Zero-trust architecture - validates JWT on every request
 * SOC 2 Alignment: CC6.2 (Access Control), CC6.3 (Authentication)
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';
import { query } from '@/config/database';

export interface AuthenticatedUser {
  id: string;
  authProviderId: string;
  email: string;
  role: string;
}

export interface AuthenticatedRequest extends Request {
  user: AuthenticatedUser;
  body: any;
  params: any;
  query: any;
}

export interface JWTPayload {
  sub: string; // auth provider ID
  email: string;
  role?: string;
  iat: number;
  exp: number;
}

export async function authMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header'
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify JWT token
    let decoded: JWTPayload;
    try {
      decoded = jwt.verify(token, config.auth.jwtSecret) as JWTPayload;
    } catch (jwtError) {
      logger.warn('Invalid JWT token:', jwtError);
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid or expired token'
      });
      return;
    }

    // Validate required JWT claims
    if (!decoded.sub || !decoded.email) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid token claims'
      });
      return;
    }

    // Look up user in database
    const userResult = await query(
      'SELECT id, auth_provider_id, email, role FROM users WHERE auth_provider_id = $1 AND email = $2',
      [decoded.sub, decoded.email]
    );

    if (userResult.rows.length === 0) {
      // User not found - create new user record
      const newUserResult = await query(
        `INSERT INTO users (auth_provider_id, email, role) 
         VALUES ($1, $2, $3) 
         RETURNING id, auth_provider_id, email, role`,
        [decoded.sub, decoded.email, decoded.role || 'user']
      );

      if (newUserResult.rows.length === 0) {
        logger.error('Failed to create new user record');
        res.status(500).json({
          error: 'Internal Server Error',
          message: 'Failed to create user record'
        });
        return;
      }

      const newUser = newUserResult.rows[0];
      logger.info(`Created new user: ${newUser.email} (${newUser.id})`);

      // Attach user to request
      (req as AuthenticatedRequest).user = {
        id: newUser.id,
        authProviderId: newUser.auth_provider_id,
        email: newUser.email,
        role: newUser.role
      };
    } else {
      const user = userResult.rows[0];
      
      // Attach user to request
      (req as AuthenticatedRequest).user = {
        id: user.id,
        authProviderId: user.auth_provider_id,
        email: user.email,
        role: user.role
      };
    }

    // Log authentication success
    logger.debug(`User authenticated: ${(req as AuthenticatedRequest).user.email}`);

    next();
  } catch (error) {
    logger.error('Authentication middleware error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed'
    });
  }
}

// Role-based authorization middleware
export function requireRole(requiredRole: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = (req as AuthenticatedRequest).user;
    
    if (!user) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
      return;
    }

    if (user.role !== requiredRole && user.role !== 'admin') {
      res.status(403).json({
        error: 'Forbidden',
        message: `Required role: ${requiredRole}`
      });
      return;
    }

    next();
  };
}

// Workspace access authorization middleware
export async function requireWorkspaceAccess(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const user = (req as AuthenticatedRequest).user;
    const workspaceId = req.params.workspaceId;

    if (!user) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
      return;
    }

    if (!workspaceId) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Workspace ID required'
      });
      return;
    }

    // Check if user has access to workspace (owner or member)
    const accessResult = await query(
      `SELECT 1 FROM workspaces w
       WHERE w.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (
           SELECT 1 FROM workspace_members wm 
           WHERE wm.workspace_id = w.id AND wm.user_id = $2
         )
       )`,
      [workspaceId, user.id]
    );

    if (accessResult.rows.length === 0) {
      res.status(403).json({
        error: 'Forbidden',
        message: 'Access denied to workspace'
      });
      return;
    }

    next();
  } catch (error) {
    logger.error('Workspace access check error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Access check failed'
    });
  }
}

// Project access authorization middleware
export async function requireProjectAccess(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const user = (req as AuthenticatedRequest).user;
    const projectId = req.params.projectId;

    if (!user) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
      return;
    }

    if (!projectId) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Project ID required'
      });
      return;
    }

    // Check if user has access to project through workspace
    const accessResult = await query(
      `SELECT 1 FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (
           SELECT 1 FROM workspace_members wm 
           WHERE wm.workspace_id = w.id AND wm.user_id = $2
         )
       )`,
      [projectId, user.id]
    );

    if (accessResult.rows.length === 0) {
      res.status(403).json({
        error: 'Forbidden',
        message: 'Access denied to project'
      });
      return;
    }

    next();
  } catch (error) {
    logger.error('Project access check error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Access check failed'
    });
  }
}
