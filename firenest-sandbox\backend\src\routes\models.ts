/**
 * Pricing Models Routes
 * Phase 2: Pricing Model Builder
 * CRUD operations for pricing models and components
 * SOC 2 Alignment: CC6.2 (Access Control)
 */

import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { queryWithUserContext, transactionWithUserContext } from '@/config/database';
import { logger } from '@/utils/logger';
import { validate, schemas } from '@/utils/validation';
import { asyncHandler, NotFoundError, ValidationError, ConflictError } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';

const router = Router();

// Simple component validation function
function validateModelComponentConfig(componentType: string, config: any): void {
  if (!config || typeof config !== 'object') {
    throw new ValidationError('Component configuration is required');
  }

  switch (componentType) {
    case 'BASE_FEE':
      if (typeof config.amount !== 'number' || config.amount < 0) {
        throw new ValidationError('Base fee amount must be a positive number');
      }
      break;
    case 'PER_UNIT_RATE':
      if (typeof config.unitRate !== 'number' || config.unitRate < 0) {
        throw new ValidationError('Unit rate must be a positive number');
      }
      break;
    case 'TIERED_RATE':
      if (!Array.isArray(config.tiers) || config.tiers.length === 0) {
        throw new ValidationError('Tiered rate must have at least one tier');
      }
      break;
    case 'MINIMUM_FEE':
    case 'MAXIMUM_FEE':
      if (typeof config.amount !== 'number' || config.amount < 0) {
        throw new ValidationError('Fee amount must be a positive number');
      }
      break;
    default:
      throw new ValidationError(`Unknown component type: ${componentType}`);
  }
}

// Get all pricing models for a project
router.get('/project/:projectId',
  validate(Joi.object({ projectId: schemas.uuid }), 'params'),
  validate(schemas.pagination, 'query'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { projectId } = req.params;
    const { page, limit, sortBy, sortOrder } = req.query;
    const offset = (page - 1) * limit;

    // Verify project access
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT 1 FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (projectCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT pm.id, pm.name, pm.description, pm.model_type, pm.created_at, pm.updated_at,
              (SELECT COUNT(*) FROM model_components mc WHERE mc.model_id = pm.id) as component_count,
              (SELECT COUNT(*) FROM simulation_model_runs smr WHERE smr.model_id = pm.id) as simulation_count
       FROM pricing_models pm
       WHERE pm.project_id = $1
       ORDER BY ${sortBy || 'pm.created_at'} ${sortOrder}
       LIMIT $2 OFFSET $3`,
      [projectId, limit, offset]
    );

    const countResult = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT COUNT(*) as total FROM pricing_models WHERE project_id = $1',
      [projectId]
    );

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].total),
        totalPages: Math.ceil(countResult.rows[0].total / limit)
      }
    });
  })
);

// Get pricing model by ID with components
router.get('/:modelId',
  validate(Joi.object({ modelId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { modelId } = req.params;

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT pm.id, pm.name, pm.description, pm.model_type, pm.project_id, pm.created_at, pm.updated_at,
              sp.name as project_name, sp.status as project_status
       FROM pricing_models pm
       JOIN sandbox_projects sp ON pm.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE pm.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [modelId, req.user.id]
    );

    if (result.rows.length === 0) {
      throw new NotFoundError('Pricing model not found or access denied');
    }

    // Get model components
    const componentsResult = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT id, component_type, config, sort_order, created_at
       FROM model_components
       WHERE model_id = $1
       ORDER BY sort_order ASC, created_at ASC`,
      [modelId]
    );

    const model = result.rows[0];
    model.components = componentsResult.rows.map(comp => ({
      ...comp,
      config: JSON.parse(comp.config)
    }));

    res.json({
      success: true,
      data: model
    });
  })
);

// Create new pricing model
router.post('/',
  validate(schemas.createPricingModel),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { name, description, modelType, projectId } = req.body;

    // Verify project access and status
    const projectCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT sp.status FROM sandbox_projects sp
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE sp.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [projectId, req.user.id]
    );

    if (projectCheck.rows.length === 0) {
      throw new NotFoundError('Project not found or access denied');
    }

    const project = projectCheck.rows[0];
    if (project.status !== 'READY' && project.status !== 'COMPLETE') {
      throw new ValidationError('Project must have validated data before creating pricing models');
    }

    const result = await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        const modelResult = await client.query(
          `INSERT INTO pricing_models (id, name, description, model_type, project_id)
           VALUES ($1, $2, $3, $4, $5)
           RETURNING id, name, description, model_type, created_at, updated_at`,
          [uuidv4(), name, description, modelType, projectId]
        );

        return modelResult.rows[0];
      }
    );

    logger.info('Pricing model created', {
      modelId: result.id,
      userId: req.user.id,
      projectId,
      modelType,
      name
    });

    res.status(201).json({
      success: true,
      data: result
    });
  })
);

// Update pricing model
router.put('/:modelId',
  validate(Joi.object({ modelId: schemas.uuid }), 'params'),
  validate(schemas.updatePricingModel),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { modelId } = req.params;
    const { name, description, modelType } = req.body;

    // Check model access
    const accessCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT pm.name as current_name FROM pricing_models pm
       JOIN sandbox_projects sp ON pm.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE pm.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [modelId, req.user.id]
    );

    if (accessCheck.rows.length === 0) {
      throw new NotFoundError('Pricing model not found or access denied');
    }

    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    if (name !== undefined) {
      updateFields.push(`name = $${paramIndex++}`);
      updateValues.push(name);
    }
    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex++}`);
      updateValues.push(description);
    }
    if (modelType !== undefined) {
      updateFields.push(`model_type = $${paramIndex++}`);
      updateValues.push(modelType);
    }

    updateFields.push(`updated_at = NOW()`);
    updateValues.push(modelId);

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `UPDATE pricing_models 
       SET ${updateFields.join(', ')}
       WHERE id = $${paramIndex}
       RETURNING id, name, description, model_type, created_at, updated_at`,
      updateValues
    );

    logger.info('Pricing model updated', {
      modelId,
      userId: req.user.id,
      changes: { name, description, modelType }
    });

    res.json({
      success: true,
      data: result.rows[0]
    });
  })
);

// Delete pricing model
router.delete('/:modelId',
  validate(Joi.object({ modelId: schemas.uuid }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { modelId } = req.params;

    // Check model access and get model info
    const modelCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT pm.name FROM pricing_models pm
       JOIN sandbox_projects sp ON pm.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE pm.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [modelId, req.user.id]
    );

    if (modelCheck.rows.length === 0) {
      throw new NotFoundError('Pricing model not found or access denied');
    }

    // Check if model is being used in simulations
    const simulationCheck = await queryWithUserContext(
      req.user.authProviderId,
      'SELECT COUNT(*) as count FROM simulation_model_runs WHERE model_id = $1',
      [modelId]
    );

    if (parseInt(simulationCheck.rows[0].count) > 0) {
      throw new ConflictError('Cannot delete pricing model that is used in simulations');
    }

    await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        // Delete model components first
        await client.query('DELETE FROM model_components WHERE model_id = $1', [modelId]);
        // Delete the model
        await client.query('DELETE FROM pricing_models WHERE id = $1', [modelId]);
      }
    );

    logger.info('Pricing model deleted', {
      modelId,
      userId: req.user.id,
      modelName: modelCheck.rows[0].name
    });

    res.json({
      success: true,
      message: 'Pricing model deleted successfully'
    });
  })
);

// Add component to pricing model
router.post('/:modelId/components',
  validate(Joi.object({ modelId: schemas.uuid }), 'params'),
  validate(schemas.createModelComponent),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { modelId } = req.params;
    const { componentType, config, sortOrder } = req.body;

    // Verify model access
    const modelCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT pm.id FROM pricing_models pm
       JOIN sandbox_projects sp ON pm.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE pm.id = $1 AND (
         w.owner_id = $2 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $2)
       )`,
      [modelId, req.user.id]
    );

    if (modelCheck.rows.length === 0) {
      throw new NotFoundError('Pricing model not found or access denied');
    }

    // Validate component configuration
    validateModelComponentConfig(componentType, config);

    const result = await transactionWithUserContext(
      req.user.authProviderId,
      async (client) => {
        const componentResult = await client.query(
          `INSERT INTO model_components (id, model_id, component_type, config, sort_order)
           VALUES ($1, $2, $3, $4, $5)
           RETURNING id, component_type, config, sort_order, created_at`,
          [uuidv4(), modelId, componentType, JSON.stringify(config), sortOrder || 0]
        );

        return componentResult.rows[0];
      }
    );

    logger.info('Model component added', {
      componentId: result.id,
      modelId,
      userId: req.user.id,
      componentType
    });

    res.status(201).json({
      success: true,
      data: {
        ...result,
        config: JSON.parse(result.config)
      }
    });
  })
);

// Update model component
router.put('/:modelId/components/:componentId',
  validate(Joi.object({ 
    modelId: schemas.uuid,
    componentId: schemas.uuid 
  }), 'params'),
  validate(Joi.object({
    config: Joi.object().required(),
    sortOrder: Joi.number().integer().min(0).optional()
  })),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { modelId, componentId } = req.params;
    const { config, sortOrder } = req.body;

    // Verify component access and get component type
    const componentCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT mc.component_type FROM model_components mc
       JOIN pricing_models pm ON mc.model_id = pm.id
       JOIN sandbox_projects sp ON pm.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE mc.id = $1 AND mc.model_id = $2 AND (
         w.owner_id = $3 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $3)
       )`,
      [componentId, modelId, req.user.id]
    );

    if (componentCheck.rows.length === 0) {
      throw new NotFoundError('Model component not found or access denied');
    }

    // Validate component configuration
    validateModelComponentConfig(componentCheck.rows[0].component_type, config);

    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    updateFields.push(`config = $${paramIndex++}`);
    updateValues.push(JSON.stringify(config));

    if (sortOrder !== undefined) {
      updateFields.push(`sort_order = $${paramIndex++}`);
      updateValues.push(sortOrder);
    }

    updateValues.push(componentId);

    const result = await queryWithUserContext(
      req.user.authProviderId,
      `UPDATE model_components 
       SET ${updateFields.join(', ')}
       WHERE id = $${paramIndex}
       RETURNING id, component_type, config, sort_order, created_at`,
      updateValues
    );

    logger.info('Model component updated', {
      componentId,
      modelId,
      userId: req.user.id
    });

    res.json({
      success: true,
      data: {
        ...result.rows[0],
        config: JSON.parse(result.rows[0].config)
      }
    });
  })
);

// Delete model component
router.delete('/:modelId/components/:componentId',
  validate(Joi.object({ 
    modelId: schemas.uuid,
    componentId: schemas.uuid 
  }), 'params'),
  asyncHandler(async (req: AuthenticatedRequest, res) => {
    const { modelId, componentId } = req.params;

    // Verify component access
    const componentCheck = await queryWithUserContext(
      req.user.authProviderId,
      `SELECT 1 FROM model_components mc
       JOIN pricing_models pm ON mc.model_id = pm.id
       JOIN sandbox_projects sp ON pm.project_id = sp.id
       JOIN workspaces w ON sp.workspace_id = w.id
       WHERE mc.id = $1 AND mc.model_id = $2 AND (
         w.owner_id = $3 OR
         EXISTS (SELECT 1 FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.user_id = $3)
       )`,
      [componentId, modelId, req.user.id]
    );

    if (componentCheck.rows.length === 0) {
      throw new NotFoundError('Model component not found or access denied');
    }

    await queryWithUserContext(
      req.user.authProviderId,
      'DELETE FROM model_components WHERE id = $1',
      [componentId]
    );

    logger.info('Model component deleted', {
      componentId,
      modelId,
      userId: req.user.id
    });

    res.json({
      success: true,
      message: 'Model component deleted successfully'
    });
  })
);

export default router;
