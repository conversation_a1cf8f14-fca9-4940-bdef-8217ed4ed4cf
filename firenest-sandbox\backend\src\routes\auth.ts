/**
 * Authentication Routes
 * JWT-based authentication with identity provider integration
 * SOC 2 Alignment: CC6.2 (Access Control), CC6.3 (Authentication)
 */

import { Router } from 'express';
import jwt from 'jsonwebtoken';
import Joi from 'joi';
import { config } from '@/config/environment';
import { query } from '@/config/database';
import { logger } from '@/utils/logger';
import { validate, schemas } from '@/utils/validation';
import { asyncHandler } from '@/middleware/errorHandler';
import { AuthenticationError, ValidationError } from '@/middleware/errorHandler';

const router = Router();

// Login endpoint - validates identity provider token and issues JWT
router.post('/login', 
  validate(Joi.object({
    idToken: Joi.string().required(),
    provider: Joi.string().valid('auth0', 'okta', 'cognito').optional().default('auth0')
  })),
  asyncHandler(async (req, res) => {
    const { idToken, provider } = req.body;

    try {
      // Verify the identity provider token
      const userInfo = await verifyIdToken(idToken, provider);
      
      // Check if user exists in our database
      let user = await findOrCreateUser(userInfo);
      
      // Generate JWT token
      const token = generateJWT(user);
      
      logger.info('User login successful', {
        userId: user.id,
        email: user.email,
        provider
      });

      res.json({
        success: true,
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        },
        expiresIn: config.auth.jwtExpiry
      });
    } catch (error) {
      logger.error('Login failed', error);
      throw new AuthenticationError('Invalid credentials');
    }
  })
);

// Token refresh endpoint
router.post('/refresh',
  validate(Joi.object({
    token: Joi.string().required()
  })),
  asyncHandler(async (req, res) => {
    const { token } = req.body;

    try {
      // Verify the existing token (even if expired)
      const decoded = jwt.verify(token, config.auth.jwtSecret, { ignoreExpiration: true }) as any;
      
      // Check if user still exists and is active
      const userResult = await query(
        'SELECT id, auth_provider_id, email, role FROM users WHERE auth_provider_id = $1',
        [decoded.sub]
      );

      if (userResult.rows.length === 0) {
        throw new AuthenticationError('User not found');
      }

      const user = userResult.rows[0];
      
      // Generate new JWT token
      const newToken = generateJWT(user);
      
      logger.info('Token refresh successful', {
        userId: user.id,
        email: user.email
      });

      res.json({
        success: true,
        token: newToken,
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        },
        expiresIn: config.auth.jwtExpiry
      });
    } catch (error) {
      logger.error('Token refresh failed', error);
      throw new AuthenticationError('Invalid or expired token');
    }
  })
);

// Logout endpoint (for audit logging)
router.post('/logout',
  asyncHandler(async (req, res) => {
    // Extract user info from token if available
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const decoded = jwt.verify(token, config.auth.jwtSecret) as any;
        
        logger.info('User logout', {
          authProviderId: decoded.sub,
          email: decoded.email
        });
      } catch (error) {
        // Ignore token verification errors for logout
      }
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  })
);

// User profile endpoint
router.get('/profile',
  asyncHandler(async (req, res) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing authorization header');
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, config.auth.jwtSecret) as any;
    
    const userResult = await query(
      'SELECT id, auth_provider_id, email, role, created_at, updated_at FROM users WHERE auth_provider_id = $1',
      [decoded.sub]
    );

    if (userResult.rows.length === 0) {
      throw new AuthenticationError('User not found');
    }

    const user = userResult.rows[0];
    
    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }
    });
  })
);

// Helper functions
async function verifyIdToken(idToken: string, provider: string): Promise<any> {
  // In a real implementation, you would verify the token with the identity provider
  // For now, we'll decode it assuming it's a valid JWT
  try {
    const decoded = jwt.decode(idToken) as any;
    
    if (!decoded || !decoded.sub || !decoded.email) {
      throw new Error('Invalid token structure');
    }

    return {
      authProviderId: decoded.sub,
      email: decoded.email,
      name: decoded.name,
      role: decoded.role || 'user'
    };
  } catch (error) {
    throw new AuthenticationError('Invalid identity token');
  }
}

async function findOrCreateUser(userInfo: any): Promise<any> {
  // Check if user exists
  let userResult = await query(
    'SELECT id, auth_provider_id, email, role FROM users WHERE auth_provider_id = $1',
    [userInfo.authProviderId]
  );

  if (userResult.rows.length > 0) {
    return userResult.rows[0];
  }

  // Create new user
  userResult = await query(
    `INSERT INTO users (auth_provider_id, email, role) 
     VALUES ($1, $2, $3) 
     RETURNING id, auth_provider_id, email, role`,
    [userInfo.authProviderId, userInfo.email, userInfo.role]
  );

  if (userResult.rows.length === 0) {
    throw new Error('Failed to create user');
  }

  const newUser = userResult.rows[0];
  
  logger.info('New user created', {
    userId: newUser.id,
    email: newUser.email,
    role: newUser.role
  });

  return newUser;
}

function generateJWT(user: any): string {
  const payload = {
    sub: user.auth_provider_id,
    email: user.email,
    role: user.role,
    iat: Math.floor(Date.now() / 1000)
  };

  return jwt.sign(payload, config.auth.jwtSecret, {
    expiresIn: config.auth.jwtExpiry
  });
}

export default router;
