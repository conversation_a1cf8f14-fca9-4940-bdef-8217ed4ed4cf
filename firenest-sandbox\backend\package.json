{"name": "firenest-sandbox-backend", "version": "1.0.0", "description": "Firenest Sandbox Backend API - Pricing Intelligence Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "dev:worker": "nodemon src/workers/index.ts", "build": "tsc", "start": "node dist/index.js", "start:worker": "node dist/workers/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "migrate": "node dist/scripts/migrate.js", "seed": "node dist/scripts/seed.js"}, "keywords": ["firenest", "sandbox", "pricing", "simulation", "aws", "postgresql"], "author": "Firenest", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "pg-pool": "^3.6.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "uuid": "^9.0.1", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "aws-sdk": "^2.1490.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "bull": "^4.12.2", "redis": "^4.6.10", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"@types/node": "^20.8.9", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.7", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^9.0.6", "@types/multer": "^1.4.11", "@types/jest": "^29.5.6", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.52.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0"}, "engines": {"node": ">=18.0.0"}}