/**
 * Background Validation Worker
 * Phase 1: Secure Data Ingestion
 * Processes uploaded files and validates data quality
 * SOC 2 Alignment: CC7.1 (System Operations), A1.2 (Data Integrity)
 */

import AWS from 'aws-sdk';
import csvParser from 'csv-parser';
import { Readable } from 'stream';
import { config } from '@/config/environment';
import { query, transaction } from '@/config/database';
import { logger } from '@/utils/logger';

// Configure AWS services
const s3 = new AWS.S3({
  accessKeyId: config.aws.accessKeyId,
  secretAccessKey: config.aws.secretAccessKey,
  region: config.aws.region
});

const sqs = new AWS.SQS({
  accessKeyId: config.aws.accessKeyId,
  secretAccessKey: config.aws.secretAccessKey,
  region: config.aws.region
});

export interface ValidationJob {
  jobType: 'VALIDATE_UPLOAD';
  uploadId: string;
  projectId: string;
  s3Key: string;
  fileType: string;
  userId: string;
  timestamp: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  metadata: {
    rowCount: number;
    columnCount: number;
    fileSize: number;
    processingTime: number;
    dataQuality: {
      completeness: number;
      consistency: number;
      accuracy: number;
    };
  };
}

export interface ValidationError {
  type: 'MISSING_COLUMNS' | 'INVALID_FORMAT' | 'DATA_TYPE_MISMATCH' | 'DUPLICATE_RECORDS' | 'INVALID_VALUES';
  message: string;
  details: any;
  severity: 'ERROR' | 'WARNING';
  row?: number;
  column?: string;
}

export interface ValidationWarning {
  type: 'DATA_QUALITY' | 'PERFORMANCE' | 'RECOMMENDATION';
  message: string;
  details: any;
  impact: 'LOW' | 'MEDIUM' | 'HIGH';
}

// Required columns for each file type
const REQUIRED_COLUMNS = {
  customer_usage_data: [
    'customer_id',
    'usage_date',
    'metric_name',
    'metric_value',
    'unit'
  ],
  billing_data: [
    'customer_id',
    'billing_period_start',
    'billing_period_end',
    'total_amount',
    'currency'
  ],
  customer_metadata: [
    'customer_id',
    'customer_name',
    'plan_type',
    'signup_date'
  ]
};

export class ValidationWorker {
  private isRunning = false;
  private pollInterval = 5000; // 5 seconds

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Validation worker is already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting validation worker');

    while (this.isRunning) {
      try {
        await this.pollForJobs();
        await this.sleep(this.pollInterval);
      } catch (error) {
        logger.error('Error in validation worker:', error);
        await this.sleep(this.pollInterval);
      }
    }
  }

  stop(): void {
    this.isRunning = false;
    logger.info('Stopping validation worker');
  }

  private async pollForJobs(): Promise<void> {
    try {
      const result = await sqs.receiveMessage({
        QueueUrl: config.aws.sqs.queueUrl,
        MaxNumberOfMessages: 10,
        WaitTimeSeconds: 20,
        MessageAttributeNames: ['All']
      }).promise();

      if (result.Messages && result.Messages.length > 0) {
        logger.info(`Received ${result.Messages.length} validation jobs`);

        for (const message of result.Messages) {
          await this.processJob(message);
        }
      }
    } catch (error) {
      logger.error('Failed to poll for jobs:', error);
    }
  }

  private async processJob(message: AWS.SQS.Message): Promise<void> {
    try {
      if (!message.Body) {
        throw new Error('Message body is empty');
      }

      const job: ValidationJob = JSON.parse(message.Body);
      logger.info('Processing validation job:', { uploadId: job.uploadId });

      // Update upload status to VALIDATING
      await this.updateUploadStatus(job.uploadId, 'VALIDATING');

      // Perform validation
      const validationResult = await this.validateFile(job);

      // Update upload with validation results
      await this.updateUploadWithResults(job.uploadId, validationResult);

      // Update project status if all uploads are validated
      await this.updateProjectStatusIfReady(job.projectId);

      // Delete message from queue
      if (message.ReceiptHandle) {
        await sqs.deleteMessage({
          QueueUrl: config.aws.sqs.queueUrl,
          ReceiptHandle: message.ReceiptHandle
        }).promise();
      }

      logger.info('Validation job completed successfully:', { uploadId: job.uploadId });

    } catch (error) {
      logger.error('Failed to process validation job:', error);

      // Move to DLQ or update status to error
      if (message.Body) {
        const job: ValidationJob = JSON.parse(message.Body);
        await this.updateUploadStatus(job.uploadId, 'INVALID', [
          {
            type: 'INVALID_FORMAT',
            message: 'Failed to process file',
            details: { error: error instanceof Error ? error.message : 'Unknown error' },
            severity: 'ERROR'
          }
        ]);
      }
    }
  }

  private async validateFile(job: ValidationJob): Promise<ValidationResult> {
    const startTime = Date.now();
    
    try {
      // Download file from S3
      const fileStream = await this.downloadFileFromS3(job.s3Key);
      
      // Validate based on file type
      let validationResult: ValidationResult;
      
      if (job.s3Key.toLowerCase().endsWith('.csv')) {
        validationResult = await this.validateCSVFile(fileStream, job.fileType);
      } else if (job.s3Key.toLowerCase().endsWith('.json')) {
        validationResult = await this.validateJSONFile(fileStream, job.fileType);
      } else {
        throw new Error('Unsupported file format');
      }

      validationResult.metadata.processingTime = Date.now() - startTime;

      logger.info('File validated', {
        uploadId: job.uploadId,
        projectId: job.projectId,
        isValid: validationResult.isValid,
        errorCount: validationResult.errors.length,
        warningCount: validationResult.warnings.length,
        fileSize: validationResult.metadata.fileSize
      });

      return validationResult;

    } catch (error) {
      logger.error('File validation failed:', error);
      throw error;
    }
  }

  private async downloadFileFromS3(s3Key: string): Promise<Readable> {
    try {
      const response = await s3.getObject({
        Bucket: config.aws.s3.bucketName,
        Key: s3Key
      }).promise();

      if (!response.Body) {
        throw new Error('File not found in S3');
      }

      return Readable.from(response.Body as Buffer);
    } catch (error) {
      logger.error('Failed to download file from S3:', error);
      throw error;
    }
  }

  private async validateCSVFile(fileStream: Readable, fileType: string): Promise<ValidationResult> {
    return new Promise((resolve, reject) => {
      const errors: ValidationError[] = [];
      const warnings: ValidationWarning[] = [];
      const rows: any[] = [];
      let headers: string[] = [];
      let rowCount = 0;
      let fileSize = 0;

      fileStream
        .pipe(csvParser())
        .on('headers', (headerList: string[]) => {
          headers = headerList;
          
          // Validate required columns
          const requiredColumns = REQUIRED_COLUMNS[fileType as keyof typeof REQUIRED_COLUMNS];
          if (requiredColumns) {
            const missingColumns = requiredColumns.filter(col => !headers.includes(col));
            if (missingColumns.length > 0) {
              errors.push({
                type: 'MISSING_COLUMNS',
                message: `Missing required columns: ${missingColumns.join(', ')}`,
                details: { missingColumns, requiredColumns },
                severity: 'ERROR'
              });
            }
          }
        })
        .on('data', (row: any) => {
          rowCount++;
          rows.push(row);
          fileSize += JSON.stringify(row).length;

          // Validate row data
          this.validateRowData(row, rowCount, fileType, errors, warnings);

          // Limit memory usage for large files
          if (rows.length > 10000) {
            rows.shift();
          }
        })
        .on('end', () => {
          // Calculate data quality metrics
          const dataQuality = this.calculateDataQuality(rows, headers, fileType);

          // Add performance warnings for large files
          if (rowCount > 100000) {
            warnings.push({
              type: 'PERFORMANCE',
              message: 'Large dataset detected. Processing may take longer.',
              details: { rowCount },
              impact: 'MEDIUM'
            });
          }

          const result: ValidationResult = {
            isValid: errors.length === 0,
            errors,
            warnings,
            metadata: {
              rowCount,
              columnCount: headers.length,
              fileSize,
              processingTime: 0, // Will be set by caller
              dataQuality
            }
          };

          resolve(result);
        })
        .on('error', (error) => {
          reject(error);
        });
    });
  }

  private async validateJSONFile(fileStream: Readable, fileType: string): Promise<ValidationResult> {
    // Implementation for JSON validation
    // Similar structure to CSV validation but for JSON format
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // For now, basic JSON validation
    return {
      isValid: true,
      errors,
      warnings,
      metadata: {
        rowCount: 0,
        columnCount: 0,
        fileSize: 0,
        processingTime: 0,
        dataQuality: {
          completeness: 100,
          consistency: 100,
          accuracy: 100
        }
      }
    };
  }

  private validateRowData(
    row: any, 
    rowNumber: number, 
    fileType: string, 
    errors: ValidationError[], 
    warnings: ValidationWarning[]
  ): void {
    // Validate based on file type
    switch (fileType) {
      case 'customer_usage_data':
        this.validateUsageDataRow(row, rowNumber, errors, warnings);
        break;
      case 'billing_data':
        this.validateBillingDataRow(row, rowNumber, errors, warnings);
        break;
      case 'customer_metadata':
        this.validateCustomerMetadataRow(row, rowNumber, errors, warnings);
        break;
    }
  }

  private validateUsageDataRow(
    row: any, 
    rowNumber: number, 
    errors: ValidationError[], 
    warnings: ValidationWarning[]
  ): void {
    // Validate customer_id
    if (!row.customer_id || row.customer_id.trim() === '') {
      errors.push({
        type: 'INVALID_VALUES',
        message: 'Customer ID is required',
        details: { row: rowNumber, column: 'customer_id' },
        severity: 'ERROR',
        row: rowNumber,
        column: 'customer_id'
      });
    }

    // Validate usage_date
    if (!row.usage_date || isNaN(Date.parse(row.usage_date))) {
      errors.push({
        type: 'DATA_TYPE_MISMATCH',
        message: 'Invalid date format for usage_date',
        details: { row: rowNumber, column: 'usage_date', value: row.usage_date },
        severity: 'ERROR',
        row: rowNumber,
        column: 'usage_date'
      });
    }

    // Validate metric_value
    if (!row.metric_value || isNaN(parseFloat(row.metric_value))) {
      errors.push({
        type: 'DATA_TYPE_MISMATCH',
        message: 'Metric value must be a number',
        details: { row: rowNumber, column: 'metric_value', value: row.metric_value },
        severity: 'ERROR',
        row: rowNumber,
        column: 'metric_value'
      });
    } else if (parseFloat(row.metric_value) < 0) {
      warnings.push({
        type: 'DATA_QUALITY',
        message: 'Negative metric value detected',
        details: { row: rowNumber, column: 'metric_value', value: row.metric_value },
        impact: 'MEDIUM'
      });
    }
  }

  private validateBillingDataRow(
    row: any, 
    rowNumber: number, 
    errors: ValidationError[], 
    warnings: ValidationWarning[]
  ): void {
    // Validate total_amount
    if (!row.total_amount || isNaN(parseFloat(row.total_amount))) {
      errors.push({
        type: 'DATA_TYPE_MISMATCH',
        message: 'Total amount must be a number',
        details: { row: rowNumber, column: 'total_amount', value: row.total_amount },
        severity: 'ERROR',
        row: rowNumber,
        column: 'total_amount'
      });
    }

    // Validate currency
    const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];
    if (!row.currency || !validCurrencies.includes(row.currency.toUpperCase())) {
      warnings.push({
        type: 'DATA_QUALITY',
        message: 'Uncommon currency code detected',
        details: { row: rowNumber, column: 'currency', value: row.currency },
        impact: 'LOW'
      });
    }
  }

  private validateCustomerMetadataRow(
    row: any, 
    rowNumber: number, 
    errors: ValidationError[], 
    warnings: ValidationWarning[]
  ): void {
    // Validate customer_name
    if (!row.customer_name || row.customer_name.trim() === '') {
      warnings.push({
        type: 'DATA_QUALITY',
        message: 'Customer name is empty',
        details: { row: rowNumber, column: 'customer_name' },
        impact: 'LOW'
      });
    }

    // Validate signup_date
    if (!row.signup_date || isNaN(Date.parse(row.signup_date))) {
      errors.push({
        type: 'DATA_TYPE_MISMATCH',
        message: 'Invalid date format for signup_date',
        details: { row: rowNumber, column: 'signup_date', value: row.signup_date },
        severity: 'ERROR',
        row: rowNumber,
        column: 'signup_date'
      });
    }
  }

  private calculateDataQuality(rows: any[], headers: string[], fileType: string): any {
    if (rows.length === 0) {
      return { completeness: 0, consistency: 0, accuracy: 0 };
    }

    // Calculate completeness (percentage of non-empty values)
    let totalCells = 0;
    let filledCells = 0;

    rows.forEach(row => {
      headers.forEach(header => {
        totalCells++;
        if (row[header] && row[header].toString().trim() !== '') {
          filledCells++;
        }
      });
    });

    const completeness = totalCells > 0 ? (filledCells / totalCells) * 100 : 0;

    // Calculate consistency (basic check for data format consistency)
    const consistency = 95; // Simplified for now

    // Calculate accuracy (based on validation errors)
    const accuracy = 90; // Simplified for now

    return {
      completeness: Math.round(completeness * 100) / 100,
      consistency,
      accuracy
    };
  }

  private async updateUploadStatus(
    uploadId: string, 
    status: string, 
    validationErrors?: ValidationError[]
  ): Promise<void> {
    try {
      await query(
        `UPDATE data_uploads 
         SET status = $1, validation_errors = $2, updated_at = NOW()
         WHERE id = $3`,
        [status, JSON.stringify(validationErrors || []), uploadId]
      );
    } catch (error) {
      logger.error('Failed to update upload status:', error);
      throw error;
    }
  }

  private async updateUploadWithResults(
    uploadId: string, 
    result: ValidationResult
  ): Promise<void> {
    try {
      const status = result.isValid ? 'VALIDATED' : 'INVALID';
      
      await query(
        `UPDATE data_uploads 
         SET status = $1, validation_errors = $2, metadata = $3, updated_at = NOW()
         WHERE id = $4`,
        [
          status, 
          JSON.stringify(result.errors), 
          JSON.stringify(result.metadata),
          uploadId
        ]
      );
    } catch (error) {
      logger.error('Failed to update upload with results:', error);
      throw error;
    }
  }

  private async updateProjectStatusIfReady(projectId: string): Promise<void> {
    try {
      // Check if all uploads in project are validated
      const result = await query(
        `SELECT COUNT(*) as total,
                COUNT(CASE WHEN status IN ('VALIDATED', 'INVALID') THEN 1 END) as processed
         FROM data_uploads 
         WHERE project_id = $1`,
        [projectId]
      );

      const { total, processed } = result.rows[0];
      
      if (parseInt(total) > 0 && parseInt(total) === parseInt(processed)) {
        // Check if any uploads are invalid
        const invalidCount = await query(
          'SELECT COUNT(*) as count FROM data_uploads WHERE project_id = $1 AND status = $2',
          [projectId, 'INVALID']
        );

        const newStatus = parseInt(invalidCount.rows[0].count) > 0 ? 'ERROR' : 'READY';
        
        await query(
          'UPDATE sandbox_projects SET status = $1, updated_at = NOW() WHERE id = $2',
          [newStatus, projectId]
        );

        logger.info('Project status updated:', { projectId, status: newStatus });
      }
    } catch (error) {
      logger.error('Failed to update project status:', error);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const validationWorker = new ValidationWorker();
