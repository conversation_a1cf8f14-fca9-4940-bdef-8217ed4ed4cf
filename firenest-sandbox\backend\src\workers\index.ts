/**
 * Worker Process Entry Point
 * Starts background validation workers
 */

//import { config } from '@/config/environment';
import { connectDatabase } from '@/config/database';
import { logger } from '@/utils/logger';
import { validateEnvironment } from '@/utils/validation';
import { validationWorker } from './validationWorker';
import { simulationWorker } from './simulationWorker';

async function startWorkers() {
  try {
    // Validate environment
    validateEnvironment();
    logger.info('Environment validation passed');

    // Connect to database
    await connectDatabase();
    logger.info('Database connection established');

    // Start workers
    logger.info('Starting validation worker...');
    validationWorker.start(); // Don't await - let it run in background

    logger.info('Starting simulation worker...');
    await simulationWorker.start(); // Await the main worker

  } catch (error) {
    logger.error('Failed to start workers:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down workers gracefully');
  validationWorker.stop();
  simulationWorker.stop();
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down workers gracefully');
  validationWorker.stop();
  simulationWorker.stop();
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  validationWorker.stop();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  validationWorker.stop();
  process.exit(1);
});

// Start workers
startWorkers();
