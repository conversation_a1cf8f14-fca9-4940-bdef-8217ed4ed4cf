/**
 * Environment Configuration
 * Centralized configuration management with validation
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface Config {
  environment: string;
  port: number;
  database: {
    host: string;
    port: number;
    name: string;
    username: string;
    password: string;
    ssl: boolean;
    maxConnections: number;
    connectionTimeout: number;
  };
  aws: {
    region: string;
    accessKeyId: string;
    secretAccessKey: string;
    s3: {
      bucketName: string;
      presignedUrlExpiry: number;
    };
    sqs: {
      queueUrl: string;
      dlqUrl: string;
    };
  };
  auth: {
    jwtSecret: string;
    jwtExpiry: string;
    provider: {
      type: string;
      domain: string;
      clientId: string;
      clientSecret: string;
    };
  };
  cors: {
    allowedOrigins: string[];
  };
  logging: {
    level: string;
    cloudWatchGroup: string;
  };
  security: {
    encryptionKey: string;
    hashSalt: number;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
  };
}

export const config: Config = {
  environment: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3000', 10),
  
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    name: process.env.DB_NAME || 'firenest_sandbox',
    username: process.env.DB_USERNAME || 'sandbox_admin',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true',
    maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20', 10),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000', 10),
  },
  
  aws: {
    region: process.env.AWS_REGION || 'us-east-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
    s3: {
      bucketName: process.env.S3_BUCKET_NAME || '',
      presignedUrlExpiry: parseInt(process.env.S3_PRESIGNED_URL_EXPIRY || '300', 10), // 5 minutes
    },
    sqs: {
      queueUrl: process.env.SQS_QUEUE_URL || '',
      dlqUrl: process.env.SQS_DLQ_URL || '',
    },
  },
  
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    jwtExpiry: process.env.JWT_EXPIRY || '24h',
    provider: {
      type: process.env.AUTH_PROVIDER_TYPE || 'auth0',
      domain: process.env.AUTH_PROVIDER_DOMAIN || '',
      clientId: process.env.AUTH_PROVIDER_CLIENT_ID || '',
      clientSecret: process.env.AUTH_PROVIDER_CLIENT_SECRET || '',
    },
  },
  
  cors: {
    allowedOrigins: process.env.CORS_ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    cloudWatchGroup: process.env.CLOUDWATCH_LOG_GROUP || '/aws/ecs/firenest-sandbox',
  },
  
  security: {
    encryptionKey: process.env.ENCRYPTION_KEY || 'your-encryption-key-32-characters',
    hashSalt: parseInt(process.env.HASH_SALT || '12', 10),
  },
  
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
  },
};

// Validation function
export function validateConfig(): void {
  const requiredEnvVars = [
    'DB_PASSWORD',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'S3_BUCKET_NAME',
    'SQS_QUEUE_URL',
    'JWT_SECRET',
    'AUTH_PROVIDER_DOMAIN',
    'AUTH_PROVIDER_CLIENT_ID',
    'AUTH_PROVIDER_CLIENT_SECRET',
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // Validate JWT secret length
  if (config.auth.jwtSecret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }

  // Validate encryption key length
  if (config.security.encryptionKey.length !== 32) {
    throw new Error('ENCRYPTION_KEY must be exactly 32 characters long');
  }

  // Validate database configuration
  if (!config.database.host || !config.database.name) {
    throw new Error('Database configuration is incomplete');
  }

  // Validate AWS configuration
  if (!config.aws.region || !config.aws.s3.bucketName) {
    throw new Error('AWS configuration is incomplete');
  }
}
