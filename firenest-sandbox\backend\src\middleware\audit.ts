/**
 * Audit Middleware
 * Immutable audit trail for all actions
 * SOC 2 Alignment: CC3.2 (Audit Trails)
 */

import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';
import { query } from '@/config/database';
import { logger } from '@/utils/logger';

export interface AuditEvent {
  userId: string;
  action: string;
  targetResource: string;
  targetId?: string;
  payload?: any;
  ipAddress?: string;
  userAgent?: string;
}

export async function auditMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  const authReq = req as AuthenticatedRequest;
  
  // Skip audit for GET requests to reduce noise
  if (req.method === 'GET') {
    next();
    return;
  }

  // Store original res.json to intercept response
  const originalJson = res.json;
  let responseData: any;
  let statusCode: number;

  res.json = function(data: any) {
    responseData = data;
    statusCode = res.statusCode;
    return originalJson.call(this, data);
  };

  // Store original res.end to capture when response is sent
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: BufferEncoding, cb?: (() => void)) {
    // Log audit event after response is sent
    setImmediate(() => {
      logAuditEvent(authReq, statusCode, responseData);
    });
    return originalEnd.call(this, chunk, encoding, cb);
  };

  next();
}

async function logAuditEvent(
  req: AuthenticatedRequest,
  statusCode: number,
  responseData: any
): Promise<void> {
  try {
    if (!req.user) {
      return; // No user context, skip audit
    }

    const action = getActionFromRequest(req);
    const targetResource = getTargetResourceFromRequest(req);
    const targetId = getTargetIdFromRequest(req);
    const payload = createAuditPayload(req, statusCode, responseData);

    const auditEvent: AuditEvent = {
      userId: req.user.id,
      action,
      targetResource,
      targetId: targetId || undefined,
      payload,
      ipAddress: getClientIpAddress(req),
      userAgent: req.get('User-Agent') || undefined
    };

    await insertAuditLog(auditEvent);
  } catch (error) {
    logger.error('Failed to log audit event:', error);
    // Don't throw error to avoid disrupting the main request flow
  }
}

function getActionFromRequest(req: AuthenticatedRequest): string {
  const method = req.method;
  const path = req.route?.path || req.path;

  // Map HTTP methods and paths to semantic actions
  switch (method) {
    case 'POST':
      if (path.includes('/upload')) return 'UPLOAD_FILE';
      if (path.includes('/simulate')) return 'START_SIMULATION';
      if (path.includes('/models')) return 'CREATE_PRICING_MODEL';
      if (path.includes('/projects')) return 'CREATE_PROJECT';
      if (path.includes('/workspaces')) return 'CREATE_WORKSPACE';
      if (path.includes('/members')) return 'ADD_MEMBER';
      return 'CREATE';
    
    case 'PUT':
    case 'PATCH':
      if (path.includes('/models')) return 'UPDATE_PRICING_MODEL';
      if (path.includes('/projects')) return 'UPDATE_PROJECT';
      if (path.includes('/workspaces')) return 'UPDATE_WORKSPACE';
      return 'UPDATE';
    
    case 'DELETE':
      if (path.includes('/models')) return 'DELETE_PRICING_MODEL';
      if (path.includes('/projects')) return 'DELETE_PROJECT';
      if (path.includes('/workspaces')) return 'DELETE_WORKSPACE';
      if (path.includes('/members')) return 'REMOVE_MEMBER';
      return 'DELETE';
    
    default:
      return method;
  }
}

function getTargetResourceFromRequest(req: AuthenticatedRequest): string {
  const path = req.route?.path || req.path;

  if (path.includes('/workspaces')) return 'workspace';
  if (path.includes('/projects')) return 'project';
  if (path.includes('/models')) return 'pricing_model';
  if (path.includes('/uploads')) return 'data_upload';
  if (path.includes('/simulations')) return 'simulation';
  if (path.includes('/members')) return 'workspace_member';

  // Extract resource from path
  const pathParts = path.split('/').filter((part: string) => part && !part.startsWith(':'));
  return pathParts[pathParts.length - 1] || 'unknown';
}

function getTargetIdFromRequest(req: AuthenticatedRequest): string | undefined {
  // Extract ID from URL parameters
  const params = req.params;
  
  // Common ID parameter names
  const idParams = ['id', 'workspaceId', 'projectId', 'modelId', 'simulationId', 'uploadId'];
  
  for (const param of idParams) {
    if (params[param]) {
      return params[param];
    }
  }

  return undefined;
}

function createAuditPayload(
  req: AuthenticatedRequest,
  statusCode: number,
  responseData: any
): any {
  const payload: any = {
    method: req.method,
    path: req.path,
    statusCode,
    timestamp: new Date().toISOString()
  };

  // Include request body for non-GET requests (excluding sensitive data)
  if (req.method !== 'GET' && req.body) {
    payload.requestBody = sanitizePayload(req.body);
  }

  // Include query parameters
  if (Object.keys(req.query).length > 0) {
    payload.queryParams = req.query;
  }

  // Include response data for errors or important operations
  if (statusCode >= 400 || shouldIncludeResponseData(req)) {
    payload.responseData = sanitizePayload(responseData);
  }

  return payload;
}

function sanitizePayload(data: any): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sensitiveFields = [
    'password', 'token', 'secret', 'key', 'auth', 'authorization',
    'credit_card', 'ssn', 'social_security', 'bank_account'
  ];

  const sanitized = { ...data };

  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}

function shouldIncludeResponseData(req: AuthenticatedRequest): boolean {
  const path = req.path;
  const method = req.method;

  // Include response data for creation operations
  if (method === 'POST') return true;

  // Include response data for simulation results
  if (path.includes('/simulations') || path.includes('/results')) return true;

  return false;
}

function getClientIpAddress(req: Request): string {
  // Check for IP in various headers (for load balancers/proxies)
  const forwarded = req.get('X-Forwarded-For');
  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || 'unknown';
  }

  const realIp = req.get('X-Real-IP');
  if (realIp) {
    return realIp;
  }

  return req.ip || req.connection.remoteAddress || 'unknown';
}

async function insertAuditLog(event: AuditEvent): Promise<void> {
  try {
    await query(
      `INSERT INTO audit_logs (user_id, action, target_resource, target_id, payload, ip_address, user_agent)
       VALUES ($1, $2, $3, $4, $5, $6, $7)`,
      [
        event.userId,
        event.action,
        event.targetResource,
        event.targetId || null,
        JSON.stringify(event.payload),
        event.ipAddress || null,
        event.userAgent || null
      ]
    );
  } catch (error) {
    logger.error('Failed to insert audit log:', error);
    throw error;
  }
}

// Manual audit logging function for custom events
export async function logAuditEventManual(event: AuditEvent): Promise<void> {
  await insertAuditLog(event);
}
