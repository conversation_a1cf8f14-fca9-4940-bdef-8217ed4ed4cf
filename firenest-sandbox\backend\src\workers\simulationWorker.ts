/**
 * Simulation Worker
 * Phase 3: High-performance simulation processing engine
 * Processes customer data through pricing models with real-time progress tracking
 * SOC 2 Alignment: CC7.1 (System Operations), A1.2 (Data Integrity)
 */

import AWS from 'aws-sdk';
import { Readable } from 'stream';
import csvParser from 'csv-parser';
import { query, transaction } from '@/config/database';
import { logger } from '@/utils/logger';
import { config } from '@/config/environment';

// Configure AWS services
const s3 = new AWS.S3({
  accessKeyId: config.aws.accessKeyId,
  secretAccessKey: config.aws.secretAccessKey,
  region: config.aws.region
});

const sqs = new AWS.SQS({
  accessKeyId: config.aws.accessKeyId,
  secretAccessKey: config.aws.secretAccessKey,
  region: config.aws.region
});

export interface SimulationJob {
  jobType: 'RUN_SIMULATION';
  simulationId: string;
  projectId: string;
  modelIds: string[];
  scenarioConfig: any;
  userId: string;
  timestamp: string;
}

export interface CustomerRecord {
  customer_id: string;
  usage_data: Record<string, number>;
  billing_data: Record<string, any>;
  metadata: Record<string, any>;
}

export interface SimulationResult {
  customer_id: string;
  model_id: string;
  calculated_revenue: number;
  component_breakdown: ComponentResult[];
  billing_period: string;
}

export interface ComponentResult {
  component_type: string;
  component_config: any;
  calculated_amount: number;
  calculation_details: any;
}

export class SimulationWorker {
  private isRunning = false;
  private pollInterval = 5000; // 5 seconds

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Simulation worker is already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting simulation worker');

    while (this.isRunning) {
      try {
        await this.pollForJobs();
        await this.sleep(this.pollInterval);
      } catch (error) {
        logger.error('Error in simulation worker:', error);
        await this.sleep(this.pollInterval);
      }
    }
  }

  stop(): void {
    this.isRunning = false;
    logger.info('Stopping simulation worker');
  }

  private async pollForJobs(): Promise<void> {
    try {
      const result = await sqs.receiveMessage({
        QueueUrl: config.aws.sqs.queueUrl,
        MaxNumberOfMessages: 5,
        WaitTimeSeconds: 20,
        MessageAttributeNames: ['All']
      }).promise();

      if (result.Messages && result.Messages.length > 0) {
        logger.info(`Received ${result.Messages.length} simulation jobs`);

        for (const message of result.Messages) {
          if (message.MessageAttributes?.jobType?.StringValue === 'RUN_SIMULATION') {
            await this.processSimulationJob(message);
          }
        }
      }
    } catch (error) {
      logger.error('Failed to poll for simulation jobs:', error);
    }
  }

  private async processSimulationJob(message: AWS.SQS.Message): Promise<void> {
    try {
      if (!message.Body) {
        throw new Error('Message body is empty');
      }

      const job: SimulationJob = JSON.parse(message.Body);
      logger.info('Processing simulation job:', { simulationId: job.simulationId });

      // Update simulation status to RUNNING
      await this.updateSimulationStatus(job.simulationId, 'RUNNING', 0);

      // Load customer data and pricing models
      const customerData = await this.loadCustomerData(job.projectId);
      const pricingModels = await this.loadPricingModels(job.modelIds);

      // Process simulation for each model
      const totalModels = pricingModels.length;
      let completedModels = 0;

      for (const model of pricingModels) {
        await this.processModelSimulation(job.simulationId, model, customerData, job.userId);
        completedModels++;
        
        const progress = Math.round((completedModels / totalModels) * 100);
        await this.updateSimulationStatus(job.simulationId, 'RUNNING', progress);
      }

      // Calculate aggregated results
      await this.calculateAggregatedResults(job.simulationId);

      // Mark simulation as complete
      await this.updateSimulationStatus(job.simulationId, 'COMPLETE', 100);

      // Delete message from queue
      if (message.ReceiptHandle) {
        await sqs.deleteMessage({
          QueueUrl: config.aws.sqs.queueUrl,
          ReceiptHandle: message.ReceiptHandle
        }).promise();
      }

      logger.info('Simulation completed successfully:', {
        simulationId: job.simulationId,
        projectId: job.projectId,
        modelCount: job.modelIds.length,
        customerCount: customerData.length
      });

    } catch (error) {
      logger.error('Failed to process simulation job:', error);

      if (message.Body) {
        const job: SimulationJob = JSON.parse(message.Body);
        await this.updateSimulationStatus(job.simulationId, 'FAILED', 0, error instanceof Error ? error.message : 'Unknown error');
      }
    }
  }

  private async loadCustomerData(projectId: string): Promise<CustomerRecord[]> {
    // Get all validated data uploads for the project
    const uploadsResult = await query(
      `SELECT s3_key, file_type FROM data_uploads 
       WHERE project_id = $1 AND status = 'VALIDATED'
       ORDER BY created_at ASC`,
      [projectId]
    );

    const uploads = uploadsResult.rows;
    const customerData: Map<string, CustomerRecord> = new Map();

    // Process each upload file
    for (const upload of uploads) {
      const fileData = await this.downloadAndParseFile(upload.s3_key);
      
      for (const record of fileData) {
        const customerId = record.customer_id;
        
        if (!customerData.has(customerId)) {
          customerData.set(customerId, {
            customer_id: customerId,
            usage_data: {},
            billing_data: {},
            metadata: {}
          });
        }

        const customer = customerData.get(customerId)!;

        // Merge data based on file type
        switch (upload.file_type) {
          case 'customer_usage_data':
            customer.usage_data[record.metric_name] = parseFloat(record.metric_value) || 0;
            break;
          case 'billing_data':
            customer.billing_data = { ...customer.billing_data, ...record };
            break;
          case 'customer_metadata':
            customer.metadata = { ...customer.metadata, ...record };
            break;
        }
      }
    }

    return Array.from(customerData.values());
  }

  private async downloadAndParseFile(s3Key: string): Promise<any[]> {
    try {
      const response = await s3.getObject({
        Bucket: config.aws.s3.bucketName,
        Key: s3Key
      }).promise();

      if (!response.Body) {
        throw new Error('File not found in S3');
      }

      const fileStream = Readable.from(response.Body as Buffer);
      const records: any[] = [];

      return new Promise((resolve, reject) => {
        fileStream
          .pipe(csvParser())
          .on('data', (row) => {
            records.push(row);
          })
          .on('end', () => {
            resolve(records);
          })
          .on('error', (error) => {
            reject(error);
          });
      });
    } catch (error) {
      logger.error('Failed to download and parse file:', error);
      throw error;
    }
  }

  private async loadPricingModels(modelIds: string[]): Promise<any[]> {
    const result = await query(
      `SELECT pm.id, pm.name, pm.model_type,
              json_agg(
                json_build_object(
                  'id', mc.id,
                  'component_type', mc.component_type,
                  'config', mc.config,
                  'sort_order', mc.sort_order
                ) ORDER BY mc.sort_order ASC, mc.created_at ASC
              ) as components
       FROM pricing_models pm
       LEFT JOIN model_components mc ON pm.id = mc.model_id
       WHERE pm.id = ANY($1)
       GROUP BY pm.id, pm.name, pm.model_type
       ORDER BY pm.created_at ASC`,
      [modelIds]
    );

    return result.rows.map(model => ({
      ...model,
      components: model.components.filter((comp: any) => comp.id !== null)
    }));
  }

  private async processModelSimulation(
    simulationId: string,
    model: any,
    customerData: CustomerRecord[],
    userId: string
  ): Promise<void> {
    const modelRunId = await this.createModelRun(simulationId, model.id, customerData.length);
    
    try {
      await this.updateModelRunStatus(modelRunId, 'RUNNING', 0, customerData.length);

      const results: SimulationResult[] = [];
      let processedCount = 0;

      // Process customers in batches for better performance
      const batchSize = 100;
      for (let i = 0; i < customerData.length; i += batchSize) {
        const batch = customerData.slice(i, i + batchSize);
        
        for (const customer of batch) {
          const result = await this.calculateCustomerRevenue(customer, model);
          results.push(result);
          processedCount++;

          // Update progress every 10 customers
          if (processedCount % 10 === 0) {
            await this.updateModelRunStatus(modelRunId, 'RUNNING', processedCount, customerData.length);
          }
        }

        // Store batch results
        await this.storeBatchResults(simulationId, results.slice(i, i + batch.length));
      }

      await this.updateModelRunStatus(modelRunId, 'COMPLETE', customerData.length, customerData.length);

      logger.info('Model simulation completed', {
        simulationId,
        modelId: model.id,
        customerCount: customerData.length
      });

    } catch (error) {
      await this.updateModelRunStatus(modelRunId, 'FAILED', processedCount, customerData.length, error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  private async calculateCustomerRevenue(customer: CustomerRecord, model: any): Promise<SimulationResult> {
    const componentResults: ComponentResult[] = [];
    let totalRevenue = 0;

    // Process components in order
    for (const component of model.components) {
      const config = JSON.parse(component.config);
      let componentAmount = 0;
      let calculationDetails: any = {};

      switch (component.component_type) {
        case 'BASE_FEE':
          componentAmount = config.amount || 0;
          calculationDetails = {
            base_amount: config.amount,
            period: config.period,
            currency: config.currency
          };
          break;

        case 'PER_UNIT_RATE':
          const usage = customer.usage_data[config.metricName] || 0;
          componentAmount = usage * (config.unitRate || 0);
          calculationDetails = {
            usage_amount: usage,
            unit_rate: config.unitRate,
            metric_name: config.metricName
          };
          break;

        case 'TIERED_RATE':
          if (config.tiers && config.tiers.length > 0) {
            const usage = customer.usage_data[config.metricName] || 0;
            let remainingUsage = usage;
            let tieredAmount = 0;
            const tierBreakdown: any[] = [];

            for (let i = 0; i < config.tiers.length; i++) {
              if (remainingUsage <= 0) break;

              const tier = config.tiers[i];
              const previousLimit = i > 0 ? config.tiers[i - 1].upTo : 0;
              const tierLimit = tier.upTo === 'infinity' ? remainingUsage : Math.min(tier.upTo - previousLimit, remainingUsage);
              const tierCost = tierLimit * tier.unitRate;
              
              tieredAmount += tierCost;
              tierBreakdown.push({
                tier_index: i,
                usage_in_tier: tierLimit,
                tier_rate: tier.unitRate,
                tier_cost: tierCost
              });
              
              remainingUsage -= tierLimit;
            }

            componentAmount = tieredAmount;
            calculationDetails = {
              total_usage: usage,
              tier_breakdown: tierBreakdown,
              metric_name: config.metricName
            };
          }
          break;

        case 'MINIMUM_FEE':
          if (totalRevenue < (config.amount || 0)) {
            componentAmount = (config.amount || 0) - totalRevenue;
            calculationDetails = {
              minimum_amount: config.amount,
              current_total: totalRevenue,
              adjustment: componentAmount
            };
          }
          break;

        case 'MAXIMUM_FEE':
          if (totalRevenue + componentAmount > (config.amount || 0)) {
            componentAmount = Math.max(0, (config.amount || 0) - totalRevenue);
            calculationDetails = {
              maximum_amount: config.amount,
              would_be_total: totalRevenue + componentAmount,
              capped_amount: componentAmount
            };
          }
          break;
      }

      if (componentAmount > 0) {
        componentResults.push({
          component_type: component.component_type,
          component_config: config,
          calculated_amount: componentAmount,
          calculation_details: calculationDetails
        });
        totalRevenue += componentAmount;
      }
    }

    return {
      customer_id: customer.customer_id,
      model_id: model.id,
      calculated_revenue: totalRevenue,
      component_breakdown: componentResults,
      billing_period: 'monthly' // Default to monthly
    };
  }

  private async storeBatchResults(simulationId: string, results: SimulationResult[]): Promise<void> {
    if (results.length === 0) return;

    await transaction(async (client) => {
      for (const result of results) {
        await client.query(
          `INSERT INTO simulation_customer_results 
           (id, simulation_id, customer_id, model_id, calculated_revenue, component_breakdown, billing_period)
           VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6)`,
          [
            simulationId,
            result.customer_id,
            result.model_id,
            result.calculated_revenue,
            JSON.stringify(result.component_breakdown),
            result.billing_period
          ]
        );
      }
    });
  }

  private async calculateAggregatedResults(simulationId: string): Promise<void> {
    // Calculate aggregated results for each model
    const aggregationResult = await query(
      `SELECT 
         model_id,
         COUNT(*) as customer_count,
         SUM(calculated_revenue) as total_revenue,
         AVG(calculated_revenue) as avg_revenue_per_customer,
         MIN(calculated_revenue) as min_customer_revenue,
         MAX(calculated_revenue) as max_customer_revenue,
         PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY calculated_revenue) as median_revenue,
         PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY calculated_revenue) as q1_revenue,
         PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY calculated_revenue) as q3_revenue
       FROM simulation_customer_results
       WHERE simulation_id = $1
       GROUP BY model_id`,
      [simulationId]
    );

    // Store aggregated results
    for (const result of aggregationResult.rows) {
      await query(
        `INSERT INTO simulation_results 
         (id, simulation_id, model_id, total_revenue, customer_count, avg_revenue_per_customer,
          min_customer_revenue, max_customer_revenue, median_revenue, q1_revenue, q3_revenue)
         VALUES (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          simulationId,
          result.model_id,
          result.total_revenue,
          result.customer_count,
          result.avg_revenue_per_customer,
          result.min_customer_revenue,
          result.max_customer_revenue,
          result.median_revenue,
          result.q1_revenue,
          result.q3_revenue
        ]
      );
    }
  }

  private async createModelRun(simulationId: string, modelId: string, totalRecords: number): Promise<string> {
    const result = await query(
      `UPDATE simulation_model_runs 
       SET total_records = $1, started_at = NOW()
       WHERE simulation_id = $2 AND model_id = $3
       RETURNING id`,
      [totalRecords, simulationId, modelId]
    );
    return result.rows[0].id;
  }

  private async updateSimulationStatus(
    simulationId: string,
    status: string,
    progress: number,
    errorMessage?: string
  ): Promise<void> {
    const updateFields = ['status = $2', 'progress_percentage = $3', 'updated_at = NOW()'];
    const values = [simulationId, status, progress];
    let paramIndex = 4;

    if (status === 'RUNNING' && progress === 0) {
      updateFields.push('started_at = NOW()');
    }

    if (status === 'COMPLETE' || status === 'FAILED' || status === 'CANCELLED') {
      updateFields.push('completed_at = NOW()');
    }

    if (errorMessage) {
      updateFields.push(`error_message = $${paramIndex++}`);
      values.push(errorMessage);
    }

    await query(
      `UPDATE simulations SET ${updateFields.join(', ')} WHERE id = $1`,
      values
    );
  }

  private async updateModelRunStatus(
    modelRunId: string,
    status: string,
    recordsProcessed: number,
    totalRecords: number,
    errorMessage?: string
  ): Promise<void> {
    const updateFields = ['status = $2', 'records_processed = $3'];
    const values = [modelRunId, status, recordsProcessed];
    let paramIndex = 4;

    if (status === 'COMPLETE' || status === 'FAILED') {
      updateFields.push('completed_at = NOW()');
    }

    if (errorMessage) {
      updateFields.push(`error_message = $${paramIndex++}`);
      values.push(errorMessage);
    }

    await query(
      `UPDATE simulation_model_runs SET ${updateFields.join(', ')} WHERE id = $1`,
      values
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const simulationWorker = new SimulationWorker();
